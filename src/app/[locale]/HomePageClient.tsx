'use client';

import React, { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Locale } from '@/types';
import { scrollToElement, getQueryString } from '@/utils';
import CustomerList from '@/components/CustomerList';
import ContactForm from '@/components/ContactForm';
import { useScrollShow } from '@/hooks/useScrollAnimation';
import { useSimpleChatBot } from '@/hooks/useChatBot';
import {
  useInViewAnimation,
  serviceTextAnimation,
  serviceImageAnimation,
  processTextAnimation,
  processImageAnimation,
  staggerItem,
  staggerContainer
} from '@/hooks/useFramerMotionAnimation';

import { ScrollToTop } from '@/components/InteractiveElements';

interface HomePageClientProps {
  locale: Locale;
}

interface ServiceItem {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  alt: string;
  isVideo?: boolean;
}

interface AdvantageItem {
  icon: string;
  title: string;
  description: string;
  alt: string;
}

interface LifecycleItem {
  icon: string;
  title: string;
}

interface ProcessItem {
  title: string;
  description: string;
  image: string;
}

interface SolutionCase {
  id: string;
  title: string;
  englishTitle: string;
  image: string;
  background: string;
  challenges: string[];
  keyProblems?: string[];
  solutions: Array<{
    title: string;
    items: string[];
  }>;
  results: string[];
  testimonial: string;
}

// ServiceItemComponent 组件 - 使用framer-motion优化动画效果
function ServiceItemComponent({ service, index, onShowContactForm }: { service: ServiceItem; index: number; onShowContactForm: () => void }) {
  const { ref: textRef, isInView: textInView } = useInViewAnimation({ threshold: 0.2 });
  const { ref: imageRef, isInView: imageInView } = useInViewAnimation({ threshold: 0.2 });
  const isEven = index % 2 === 1; // v1中的nth-child(2n)对应我们的index % 2 === 1

  return (
    <div
      key={service.id}
      id={service.id}
      className="service-item flex justify-between items-center py-[80px] border-b border-gray-100 last:border-b-0"
    >
      {/* 内容区域 - 始终从左侧滑入 */}
      <motion.div
        ref={textRef}
        variants={serviceTextAnimation}
        initial="hidden"
        animate={textInView ? "visible" : "hidden"}
        className={`service-content relative w-[540px] flex-shrink-0 px-6 box-border ${
          isEven ? 'order-2' : 'order-1'
        }`}
      >
        <div className="service-title text-gray-800">
          <h3 className="text-[32px] font-semibold leading-[52px] text-gray-900 m-0 mb-2">
            {service.title}
          </h3>
          <p className="h-[24px] text-lg text-orange-500 leading-[24px] m-0 font-medium">
            {service.subtitle}
          </p>
        </div>
        <div className="service-desc mt-8">
          <p className="text-base text-gray-700 leading-8 m-0">
            {service.description}
          </p>
        </div>
        <div className="service-contact mt-8">
          <button
            className="service-contact-button bg-transparent leading-7 text-sm rounded-[25px] px-6 py-2 border border-gray-800 cursor-pointer flex items-center hover:bg-gray-800 hover:text-white transition-all duration-300 hover:shadow-lg"
            onClick={onShowContactForm}
          >
            咨询详情
            <svg className="inline-arrow ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </motion.div>

      {/* 图片/视频区域 - 始终从右侧滑入 */}
      <motion.div
        ref={imageRef}
        variants={serviceImageAnimation}
        initial="hidden"
        animate={imageInView ? "visible" : "hidden"}
        className={`figure-area relative min-h-[333px] ${
          isEven ? 'order-1' : 'order-2'
        }`}
      >
        <figure className="rounded-lg overflow-hidden shadow-lg">
          {service.isVideo ? (
            <video
              id="video-player"
              className="video-js w-[580px] h-[333px] bg-transparent rounded-lg"
              controls
              preload="auto"
            >
              <source src={service.image} type="video/mp4" />
            </video>
          ) : (
            <Image
              src={service.image}
              alt={service.alt}
              width={580}
              height={333}
              className="w-[580px] h-[333px] object-cover rounded-lg"
            />
          )}
        </figure>
      </motion.div>
    </div>
  );
}

// AdvantageItemComponent 组件
function AdvantageItemComponent({ advantage, index }: { advantage: AdvantageItem; index: number }) {
  return (
    <motion.div
      key={index}
      variants={staggerItem}
      className="advantage-item pb-10 w-[279px] bg-gradient-to-br from-orange-50 to-orange-100 rounded-[20px] text-center h-[260px] pt-12 box-border text-gray-800 hover:scale-105 hover:shadow-lg transition-transform duration-300"
    >
      <figure className="advantage-icon-area flex items-center justify-center mb-4">
        <Image
          src={advantage.icon}
          alt={advantage.alt}
          width={64}
          height={64}
          className="w-16 h-16"
        />
      </figure>
      <div className="advantage-title text-[28px] font-semibold text-gray-900 mt-4">
        {advantage.title}
      </div>
      <div className="advantage-content text-lg mt-4 text-gray-700 px-4">
        {advantage.description}
      </div>
    </motion.div>
  );
}

// LifecycleItemComponent 组件
function LifecycleItemComponent({ item, index }: { item: LifecycleItem; index: number }) {
  // 根据v1版本的偏移模式：0, 20px, 40px, 60px, 40px, 20px, 0
  const getTopOffset = (index: number) => {
    const offsets = [0, 20, 40, 60, 40, 20, 0];
    return offsets[index] || 0;
  };

  return (
    <motion.div
      key={index}
      variants={staggerItem}
      className="lifecycle-item relative flex flex-col items-center flex-1"
      style={{ top: `${getTopOffset(index)}px` }}
    >
      <div className="lifecycle-item-icon bg-white h-[152px] w-[152px] rounded-full mb-8 relative flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
        <Image
          src={item.icon}
          alt={item.title}
          width={100}
          height={100}
        />
        {index < 6 && (
          <div
            className="arrow absolute top-[67px] -right-[18px] h-[14px] w-[12px] bg-no-repeat bg-contain z-10"
            style={{ backgroundImage: 'url("/images/index/arrow.svg")' }}
          />
        )}
      </div>
      <div className="lifecycle-item-title text-center">
        <h3 className="text-lg font-semibold text-gray-900 leading-tight">
          {item.title}
        </h3>
      </div>
    </motion.div>
  );
}

// ProcessItemComponent 组件
function ProcessItemComponent({ item, index }: { item: ProcessItem; index: number }) {
  const { ref: textRef, isInView: textInView } = useInViewAnimation({ threshold: 0.2 });
  const { ref: imageRef, isInView: imageInView } = useInViewAnimation({ threshold: 0.2 });

  return (
    <div
      key={index}
      className={`process-item relative w-full h-[520px] ${index === 6 ? 'mb-0' : 'mb-[140px]'}`}
    >
      {/* 背景 */}
      <div className={`process-background absolute w-1/2 h-[520px] z-[-1] ${
        index % 2 === 1 ? 'right-0 bg-gradient-to-l from-white to-orange-50' : 'left-0 bg-gradient-to-r from-white to-orange-50'
      }`}>
        <div className={`process-num absolute top-[100px] text-[64px] text-orange-200 font-verdana leading-none ${
          index % 2 === 1 ? 'left-[50px]' : 'right-[50px]'
        }`}>
          {String(index + 1).padStart(2, '0')}
        </div>
      </div>

      {/* 内容包装器 */}
      <div className={`process-content-wrapper max-w-[1200px] mx-auto px-4 flex justify-between items-center h-[520px] ${
        index === 4 ? 'relative -top-[46px]' : ''
      } ${index === 6 ? 'pb-0 mb-0' : ''}`}>

        {/* 内容区域 - 始终从左侧滑入 */}
        <motion.div
          ref={textRef}
          variants={processTextAnimation}
          initial="hidden"
          animate={textInView ? "visible" : "hidden"}
          className={`process-content w-[520px] ${
            index % 2 === 1 ? 'order-2' : 'order-1'
          }`}
        >
          <h3 className="text-[32px] font-semibold leading-[52px] text-gray-900 mb-6">
            {item.title}
          </h3>
          <p className="text-lg text-gray-700 leading-8">
            {item.description}
          </p>
        </motion.div>

        {/* 图片区域 - 始终从右侧滑入 */}
        <motion.div
          ref={imageRef}
          variants={processImageAnimation}
          initial="hidden"
          animate={imageInView ? "visible" : "hidden"}
          className={`figure-area relative ${
            index % 2 === 1 ? 'order-1' : 'order-2'
          }`}
        >
          <figure className="rounded-lg overflow-hidden shadow-lg">
            <Image
              src={item.image}
              alt={item.title}
              width={580}
              height={333}
              className="w-[580px] h-[333px] object-cover"
            />
          </figure>
        </motion.div>
      </div>
    </div>
  );
}

// SolutionItemComponent 组件
function SolutionItemComponent({
  caseItem,
  index,
  solutionExpandStatus,
  toggleSolution,
  getSolutionDescStyle
}: {
  caseItem: SolutionCase;
  index: number;
  solutionExpandStatus: Record<string, boolean>;
  toggleSolution: (id: string) => void;
  getSolutionDescStyle: (id: string) => React.CSSProperties;
}) {
  const solutionRef = useScrollShow(100);
  const textRef = useScrollShow(200);
  const imageRef = useScrollShow(350);

  return (
    <div key={caseItem.id} ref={solutionRef} className={`solution-item  overflow-hidden mb-12 ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
      <div className="solution-wrapper grid grid-cols-1 lg:grid-cols-2 gap-0">
        <div
          ref={imageRef}
          className={`solution-figure opacity-0 transform translate-x-8 transition-all duration-800 ease-out ${
            index % 2 === 1 ? 'lg:order-2 -translate-x-8' : 'translate-x-8'
          }`}
        >
          <figure>
            <Image
              src={caseItem.image}
              alt={caseItem.title}
              width={400}
              height={100}
              className="w-full"
            />
          </figure>
        </div>
        <div
          ref={textRef}
          className={`solution-content p-10 opacity-0 transform -translate-x-8 transition-all duration-800 ease-out ${
            index % 2 === 1 ? 'lg:order-1 translate-x-8' : '-translate-x-8'
          }`}
        >
          <div className="solution-title mb-6">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3 leading-tight">
              {caseItem.title}
            </h3>
            <p className="text-base text-orange-500 font-medium">
              {caseItem.englishTitle}
            </p>
          </div>
          <div className="solution-desc text-gray-700" style={getSolutionDescStyle(caseItem.id)}>
            <div className="space-y-4">
              <div>
                <h4 className="font-bold text-gray-900 mb-2">背景与挑战</h4>
                <p className="mb-3">{caseItem.background}</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {caseItem.challenges.map((challenge: string, idx: number) => (
                    <li key={idx}>{challenge}</li>
                  ))}
                </ul>
                {caseItem.keyProblems && (
                  <div className="mt-3">
                    <p className="font-semibold mb-2">主要挑战包括：</p>
                    <ul className="list-disc list-inside space-y-1 text-sm">
                      {caseItem.keyProblems.map((problem: string, idx: number) => (
                        <li key={idx}>{problem}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              <div>
                <h4 className="font-bold text-gray-900 mb-2">SmartDeer提供的解决方案</h4>
                <div className="space-y-3">
                  {caseItem.solutions.map((solution, idx) => (
                    <div key={idx}>
                      <p className="font-semibold text-sm">{solution.title}</p>
                      <ul className="list-disc list-inside space-y-1 text-sm ml-4">
                        {solution.items.map((item: string, itemIdx: number) => (
                          <li key={itemIdx}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-bold text-gray-900 mb-2">取得的成果</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {caseItem.results.map((result: string, idx: number) => (
                    <li key={idx}>{result}</li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-bold text-gray-900 mb-2">客户评价</h4>
                <p className="text-sm italic text-gray-600">{caseItem.testimonial}</p>
              </div>
            </div>
          </div>
          <div className="solution-expand mt-4">
            <button
              onClick={() => toggleSolution(caseItem.id)}
              className="solution-toggle inline-flex items-center text-orange-500 font-medium hover:text-orange-600 transition-colors"
            >
              <span>{!solutionExpandStatus[caseItem.id] ? '展开详情' : '收起详情'}</span>
              <svg className="inline-arrow ml-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d={!solutionExpandStatus[caseItem.id] ? "M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" : "M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"} clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}



export default function HomePageClient({ locale }: HomePageClientProps) {
  const [showContactForm, setShowContactForm] = useState(false);
  const [showConsultantCode, setShowConsultantCode] = useState(false);
  const [solutionExpandStatus, setSolutionExpandStatus] = useState({
    s1: false,
    s2: false,
    s3: false,
    s4: false,
    s5: false,
    s6: false
  });

  // 首屏动画 - 首屏应该立即显示，不需要滚动触发
  const heroRef = useRef<HTMLDivElement>(null);
  const heroTextRef = useRef<HTMLDivElement>(null);
  const heroImageRef = useRef<HTMLDivElement>(null);

  // 首屏立即显示动画
  useEffect(() => {
    // 首屏文本动画
    if (heroTextRef.current) {
      setTimeout(() => {
        if (heroTextRef.current) {
          heroTextRef.current.style.opacity = '1';
          heroTextRef.current.style.transform = 'translateY(0)';
          heroTextRef.current.classList.add('animate-in');
        }
      }, 200);
    }

    // 首屏图片动画
    if (heroImageRef.current) {
      setTimeout(() => {
        if (heroImageRef.current) {
          heroImageRef.current.style.opacity = '1';
          heroImageRef.current.style.transform = 'translateX(0)';
          heroImageRef.current.classList.add('animate-in');
        }
      }, 400);
    }
  }, []);

  // 各部分动画
  const { ref: advantageListRef, isInView: advantageListInView } = useInViewAnimation({ threshold: 0.3 });
  const { ref: lifecycleListRef, isInView: lifecycleListInView } = useInViewAnimation({ threshold: 0.3 });

  // 集成聊天机器人
  const { toggleChatBot } = useSimpleChatBot('7439335660751716386');

  // 处理滚动到指定位置的逻辑
  useEffect(() => {
    const scrollTarget = getQueryString('scroll');
    if (scrollTarget) {
      setTimeout(() => {
        scrollToElement(`#${scrollTarget}`);
      }, 100);
    }
  }, []);

  const toggleSolution = (key: string) => {
    setSolutionExpandStatus(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const getSolutionDescStyle = (key: string) => {
    const isExpanded = solutionExpandStatus[key as keyof typeof solutionExpandStatus];
    return {
      maxHeight: isExpanded ? '2000px' : '418px',
      overflow: 'hidden',
      transition: 'max-height 1s ease-out'
    };
  };

  const smoothScrollTo = (_duration: number, top: number) => {
    window.scrollTo({
      top,
      behavior: 'smooth'
    });
  };

  const toggleChat = () => {
    toggleChatBot();
  };

  const getContent = () => {
    const content = {
      zh: {
        hero: {
          slogan: '全球招&全球雇',
          title: '全球人力资源一站式服务',
          description: '招纳全球优秀人才，处理全球雇佣合规与薪酬发放问题，提供专业的人力资源一站式服务解决方案。'
        },
        customer: {
          title: '服务全球海量客户',
          subtitle: 'Serve Global Customers'
        },
        services: {
          title: '全球人力服务 & 全球 HR Saas',
          subtitle: 'Global HR Services & SaaS',
          items: [
            {
              id: 'service-recruitment',
              title: '全球人才招聘服务',
              subtitle: 'Global Recruitment',
              description: '在目标市场有用人需求，期待寻求合适人才，部署全球团队。以丰富人才库储备为基础，多年经验猎头团队实现精准人才搜寻与交付，24小时响应机制高效服务客户，专业研发团队提供技术支持。',
              image: '/images/index/recruitment.webp',
              alt: '全球人才招聘服务 Global Recruitment'
            },
            {
              id: 'service-eor',
              title: '全球名义雇主服务',
              subtitle: 'Employer of Record',
              description: '出海目标国家暂无法律主体，需要合规雇佣全职员工，省去境外设立与经营的费用，最低成本部署全球团队。可为全职用工提供合规、签证、签约入职、薪酬福利、团队管理、税务保险等全生命周期服务，企业系统平台帮助实现报表数据统计及自动化流程管理。',
              image: '/images/index/eor.webp',
              alt: '全球名义雇主服务 Employer of Record'
            },
            {
              id: 'service-contractor',
              title: '全球灵活用工服务',
              subtitle: 'Independent Contractor',
              description: '在目标国家市场有短期灵活用工诉求，暂无法律主体，雇佣独立承包商提供短期服务能力。可提供合规手续与模板化合同，通过受监督认可的方式与海外员工完成100%符合当地法律要求的线上签约，签约前对员工进行合规背景审查，最大化地避免了入职过程中可能产生的摩擦与后续的法律风险，并可支持计薪发薪服务，换汇费用透明。',
              image: '/images/index/contractor.webp',
              alt: '全球灵活用工服务 Independent Contractor'
            },
            {
              id: 'service-peo',
              title: '全球人力资源服务',
              subtitle: 'Human Resource Outsourcing',
              description: '在目标市场已建有法律主体，期待将部分或全部人力资源职能外包给专业团队运营。可提供计薪发薪、个税管理、强制保险、员工福利、员工入离职调转等服务，支持可选定制化解决方案，高效、低成本、准确地帮助企业应对不同国家或地区的法律法规。',
              image: '/images/index/peo.webp',
              alt: '全球人力资源服务 Human Resource Outsourcing'
            },
            {
              id: 'service-fintech',
              title: '金融科技驱动的全球薪资解决方案',
              subtitle: 'FinTech-Driven Global Payroll Solutions',
              description: 'SmartDeer 平台支持处理超过 150 种货币的薪资支付，可高效地将收入打款至员工和承包商的银行账户，显著降低国际转账成本。同时，SmartDeer 具备强大的批量支付功能、具有竞争力的汇率和强大的锁汇能力，有效降低汇率波动对薪资支出的影响。此外，SmartDeer 在合规与税务管理方面的专业能力，确保了全球化人力资源运营的顺畅，是国际雇佣解决方案的可靠合作伙伴。',
              image: '/images/index/fintech.png',
              alt: '金融科技驱动 FinTech-Driven'
            },
            {
              id: 'service-saas',
              title: '全球 HR Saas',
              subtitle: 'Global HR SaaS',
              description: 'SmartDeer 通过"人力服务+Saas系统"的模式为企业全球化布局提供专业的一体化解决方案，SmartDeer Global HR Saas 主要为企业实现全球人力资源数智化管理。让全球员工、全球HR和Line Manager, 可以高效地处理人事流程和相关事务, 让全球人力资源核心数据可以统一维护管理。',
              image: 'https://static.smartdeer.com/Global_HR_SaaS.mp4',
              alt: '全球 HR Saas',
              isVideo: true
            }
          ]
        },
        advantages: {
          title: '核心优势',
          subtitle: 'Why choose us?',
          items: [
            {
              icon: '/images/index/icon-global.svg',
              title: '全球覆盖',
              description: '服务网点覆盖150+个国家',
              alt: '覆盖150+个国家'
            },
            {
              icon: '/images/index/icon-professional-team.svg',
              title: '专业合规团队',
              description: '确保全球政策，用户数据合规',
              alt: '专业合规团队'
            },
            {
              icon: '/images/index/icon-service.svg',
              title: '24小时全天响应',
              description: '7x24小时响应中英文双语服务',
              alt: '24小时全中文响应'
            },
            {
              icon: '/images/index/icon-price.svg',
              title: '优势的价格',
              description: '极具竞争优势的服务费用',
              alt: '极具竞争优势的价格'
            }
          ]
        },
        lifecycle: {
          title: '全生命周期管理',
          subtitle: 'Full-Life Cycle Management',
          items: [
            { icon: '/images/index/recruitment.svg', title: '招聘' },
            { icon: '/images/index/compliance.svg', title: '合规' },
            { icon: '/images/index/contract.svg', title: '签约' },
            { icon: '/images/index/on-boarding.svg', title: '入职' },
            { icon: '/images/index/management.svg', title: '管理' },
            { icon: '/images/index/payment.svg', title: '支付' },
            { icon: '/images/index/off-boarding.svg', title: '离职' }
          ]
        },
        process: {
          title: '服务流程',
          subtitle: 'How It Works',
          items: [
            {
              num: '01',
              title: '团队覆盖全球、涉及领域广泛、实现本地化招聘',
              description: '80+猎头顾问，覆盖中国大陆、东南亚、北美、中东等多国家与地区，涉及IT、Saas、游戏、智能制造等行业，擅长运营、BD、sales、工程师等热门岗位，在全球实现本地化招聘业务。',
              image: '/images/index/recriument.png',
              alt: '团队覆盖全球、涉及领域广泛、实现本地化招聘'
            },
            {
              num: '02',
              title: '提供签证服务、合规背景调查、办理合规手续',
              description: '与客户达成合规合作协议，为员工提供本地化合规模板，并办理各类合规手续，最大程度避免入职过程中可能产生的摩擦与后续的法律风险，支持签证办理、背景调查等可选服务。',
              image: '/images/index/compliance.webp',
              alt: '提供签证服务、合规背景调查、办理合规手续'
            },
            {
              num: '03',
              title: '选择合约模板、自助创建合同、3分钟完成签订',
              description: '根据客户情况与用人需求，选择多国合规合约模板，敲击键盘，3分钟完成起草到签约，专业团队帮您审核，支持电子签名效力，在线完成签约过程。',
              image: '/images/index/contract.webp',
              alt: '选择合约模板、自助创建合同、3分钟完成签订'
            },
            {
              num: '04',
              title: '提供个人资料、线上自动审核、员工自助入职',
              description: '邀请员工登录系统，上传个人资料，平台自动审核，线上完成自助入职流程，保障员工按时入职。',
              image: '/images/index/staff.webp',
              alt: '提供个人资料、线上自动审核、员工自助入职'
            },
            {
              num: '05',
              title: '在线合约管理、在线员工档案、考勤假期统计',
              description: '跟进合约签订进度，查看员工档案，实现人事变动，支持多种打卡方式和审批流程自动化，实时管控员工考勤与休假情况。',
              image: '/images/index/progress.webp',
              alt: '在线合约管理、在线员工档案、考勤假期统计'
            },
            {
              num: '06',
              title: '薪酬项目维护、定期计薪发薪、缴纳保险福利',
              description: '报销、津贴、费用自助申请，完成线上审批，根据各国各地区复杂的计薪管理需求和法律法规，自动生成账单，支持多币种薪酬的支付，换汇费用透明。',
              image: '/images/index/pay.webp',
              alt: '薪酬项目维护、定期计薪发薪、缴纳保险福利'
            },
            {
              num: '07',
              title: '协调离职事宜、清算离职费用、办理离职手续',
              description: '客户提前特定日期提交离职申请，平台协调员工的离职事宜并清算离职费用，按照当地规定办理合规的离职手续。',
              image: '/images/index/dimission.png',
              alt: '协调离职事宜、清算离职费用、办理离职手续'
            }
          ]
        },
        solution: {
          title: '行业案例',
          subtitle: 'Solution',
          cases: [
            {
              id: 's1',
              title: '一家中国领先的 ICT 解决方案供应商 —— 全球团队扩展与管理的最佳实践',
              englishTitle: 'Client Success: A Leading Chinese ICT Solutions Provider – Best Practices in Global Team Expansion and Management',
              image: '/images/index/case1.png',
              background: '自2020年启动全球化扩张以来，该企业已进入30多个国家和地区，面临以下具体需求：',
              challenges: [
                '全球雇佣：16个国家的EOR雇佣服务和签证办理，5个国家的HRO服务',
                '雇主成本评估：精准计算多国雇主成本，并根据最新政策提供专业建议',
                '政策咨询与快速响应：跟上全球不断变化的雇佣政策，快速响应'
              ],
              keyProblems: [
                '复杂的行政任务：海外HR团队被日常咨询压得喘不过气，需要快速准确的回应',
                '签证申请困难：目标国家涉及高风险和复杂的签证流程',
                '报销工作量大：业务部门大量集中的报销申请给HR和财务团队带来压力'
              ],
              solutions: [
                {
                  title: 'EOR和HRO服务',
                  items: [
                    '在16个国家提供EOR服务和签证支持',
                    '在5个国家提供本地化HRO服务，包括薪资管理、员工福利和劳动合同处理'
                  ]
                },
                {
                  title: '实时政策咨询',
                  items: [
                    'SmartDeer专家团队提供多国雇佣政策和税务法规的实时建议，使客户能够快速调整招聘策略'
                  ]
                },
                {
                  title: '签证支持服务',
                  items: [
                    '全面的签证申请协助，从文件准备到提交，专注于高复杂度国家并提高批准率'
                  ]
                },
                {
                  title: '自动化数据管理',
                  items: [
                    'SmartDeer平台通过自动化数据提交和分析简化了报销工作流程，减少了人工工作'
                  ]
                },
                {
                  title: '高效支持机制',
                  items: [
                    '分配专门的客户经理和24/7支持团队，确保及时解决复杂问题，响应时间缩短至24小时以内'
                  ]
                }
              ],
              results: [
                '高效的全球运营：成功帮助企业在16个国家招聘团队并完成签证处理，确保无缝市场进入',
                '时间成本降低40%：自动化显著减少了HR团队的行政工作量',
                '签证批准率95%：专家支持提高了高难度地区的签证成功率',
                '报销效率提升50%：自动化数据处理优化了业务团队的报销流程'
              ],
              testimonial: '"SmartDeer为我们的全球扩张提供了全面支持。从雇佣服务到签证处理，他们展现了卓越的专业性和效率。SmartDeer是我们国际业务中不可或缺的战略合作伙伴。" —— 国际业务负责人'
            },
            {
              id: 's2',
              title: '一家中国知名游戏开发公司 —— 从 0 到 1 的日本市场成功之路',
              englishTitle: 'A Leading Chinese Game Development Company – Pioneering Success in the Japanese Market',
              image: '/images/index/case2.png',
              background: '2022年，这家年轻的中国游戏开发公司在日本注册了法律实体，2023年正式开始进军日本市场。作为首次出海，企业对日本市场充满不确定性，需要快速构建本地团队并应对严格的劳动法规和复杂的HR要求。',
              challenges: [
                '缺乏本地HR专业知识：企业没有内部HR团队具备日本劳动合规和员工管理流程的专业知识',
                '复杂的劳动法规：日本严格的劳动法要求在所有人事和劳动管理活动中严格合规，以避免法律风险'
              ],
              solutions: [
                {
                  title: '全面的HRO服务',
                  items: [
                    '制定并提交符合当地要求的员工管理政策',
                    '简化入职工作流程并准备必要的HR文档',
                    '建立数据使用政策，确保符合日本关于员工个人信息的法律'
                  ]
                },
                {
                  title: '本地法律和合规支持',
                  items: [
                    '为雇佣合同、薪资处理和社会保险管理提供实际支持',
                    '提供劳动法更新的实时建议，帮助企业应对日本的法律环境'
                  ]
                },
                {
                  title: '招聘和团队建设',
                  items: [
                    '利用SmartDeer广泛的本地网络招聘高素质人才',
                    '支持快速组建功能团队以启动业务运营'
                  ]
                },
                {
                  title: '定制化支持服务',
                  items: [
                    '提供针对企业初创阶段的定向现场支持，确保顺利过渡到日本市场'
                  ]
                }
              ],
              results: [
                '快速团队组建：在三个月内成功招聘并入职了日本本地团队，确保及时的业务启动',
                '合规保证：实现了对日本劳动法规的完全合规，消除了潜在的法律风险',
                '时间和成本节省40%：自动化和简化的HR流程显著降低了运营成本和行政工作量'
              ],
              testimonial: '"SmartDeer团队不仅提供了标准化解决方案，还提供了深度理解我们作为首次进入日本需求的定制化服务。他们的专业知识和专业精神使我们在日本市场从零到一的旅程顺利且高效。" —— 客户公司HR负责人'
            },
            {
              id: 's3',
              title: '一家领先的智能机器人解决方案提供商 —— 简化全球薪资管理',
              englishTitle: 'A Leading Smart Robotics Solutions Provider – Streamlined Global Payroll Management',
              image: '/images/index/case3.png',
              background: '作为智能机器人行业的全球领导者，该公司近年来在多个行业和地区快速扩张业务。随着国际团队的不断壮大，公司在管理全球薪资方面面临重大挑战：',
              challenges: [
                '多样化的薪资法规：各国薪资法律、税收政策和员工福利的复杂差异',
                '分散的支付流程：协调多个国际薪资支付渠道增加了时间和运营成本',
                '团队管理压力：HR团队在大规模、多地区薪资计算和数据管理方面遇到困难'
              ],
              keyProblems: [
                '合规保证：确保薪资流程遵守当地法律以降低合规风险',
                '成本优化：减少汇率波动和国际转账费用对薪资成本的影响',
                '效率提升：提高薪资处理效率，确保全球员工及时收到薪资'
              ],
              solutions: [
                {
                  title: '全球薪资服务',
                  items: [
                    '通过SmartDeer平台简化多币种薪资计算和支付，简化国际支付工作流程'
                  ]
                },
                {
                  title: '税务和合规支持',
                  items: [
                    '提供多国最新薪资和税务政策的实时监控和应用，确保合规'
                  ]
                },
                {
                  title: '汇率管理',
                  items: [
                    '提供先进的货币锁定服务，减轻汇率波动并降低支付成本'
                  ]
                },
                {
                  title: '自动化薪资管理',
                  items: [
                    '部署智能工具进行薪资计算和发放，显著减少HR团队的重复性任务并提高运营效率'
                  ]
                },
                {
                  title: '定制化报告和分析',
                  items: [
                    '提供详细的薪资报告和多维数据分析，使公司能够有效控制全球劳动成本'
                  ]
                }
              ],
              results: [
                '零合规风险：在10+个国家管理薪资，同时确保完全符合当地法规',
                '成本节省30%：优化汇率和集中支付流程显著降低了国际薪资成本',
                '效率提升50%：自动化薪资工具减轻了HR工作量，提高了整体效率',
                '员工满意度提升：确保全球团队及时收到薪资，提高了忠诚度和工作满意度'
              ],
              testimonial: '"SmartDeer的全球薪资管理服务使我们能够快速扩张，同时确保薪资运营的合规性和效率。他们的专业团队和先进工具是我们全球战略的重要支撑。" —— 客户公司HR负责人'
            },
            {
              id: 's4',
              title: '一家领先的生鲜电商平台 —— 中东扩张的全面招聘和雇佣支持',
              englishTitle: 'A Leading Fresh Food E-Commerce Platform – Comprehensive Recruitment and Employment Support for Middle East Expansion',
              image: '/images/index/case4.png',
              background: '作为中国顶级生鲜电商平台之一，该公司在国内市场取得了显著成功，并于2023年将目光投向中东。为了建立强大的立足点，公司需要建立一个本地化团队，包括采购、运营、业务分析和营销专业人员，同时派遣中国关键人员支持在沙特阿拉伯的初期运营。',
              challenges: [
                '实体建立和合规：在沙特阿拉伯没有法律实体，直接雇佣员工存在法律风险。此外，公司需要满足沙特劳动法要求，如本土化比例（沙特化）',
                '复杂的招聘需求：中东人才市场分布不均，为采购、运营、业务分析和营销角色招聘高技能人才具有挑战性',
                '跨境员工部署：从中国派遣员工到沙特阿拉伯需要处理复杂的流程，如签证申请、工作卡发放和遵守当地法规'
              ],
              solutions: [
                {
                  title: 'EOR服务支持',
                  items: [
                    'SmartDeer的本地实体直接雇佣公司员工，消除了客户建立法律实体的需要',
                    '这确保了合规性，同时绕过了沙特化要求，使公司能够快速在沙特阿拉伯和更广泛的中东地区启动运营'
                  ]
                },
                {
                  title: '区域招聘服务',
                  items: [
                    '利用SmartDeer在中东的广泛招聘网络，公司快速招聘了采购、运营、业务分析和营销角色的关键人才，覆盖沙特阿拉伯和该地区其他主要市场'
                  ]
                },
                {
                  title: '跨境部署管理',
                  items: [
                    'SmartDeer为从中国派遣的员工提供全流程支持，包括签证申请、工作卡处理和劳动文件准备，确保高效合规的跨境部署'
                  ]
                },
                {
                  title: '持续支持和咨询',
                  items: [
                    'SmartDeer的本地团队提供劳动合同管理、薪资处理和法规咨询的日常支持，减少客户在中东的HR管理负担'
                  ]
                }
              ],
              results: [
                '快速团队组建：在2个月内完成中东关键角色的招聘，成功部署10名中国员工到沙特阿拉伯，实现顺利的业务运营',
                '零合规风险：通过SmartDeer基于实体的雇佣模式，公司满足了法律要求，确保在沙特阿拉伯和其他区域市场的完全合规',
                '时间和成本优化：避免建立法律实体节省了大量时间和财务资源，使公司能够专注于核心业务运营',
                '区域效率提升：在SmartDeer的全面支持下，公司在沙特阿拉伯和更广泛的中东地区实现了高效的市场扩张，成功达到了初期运营目标'
              ],
              testimonial: '"SmartDeer的支持使我们在沙特阿拉伯和中东的扩张变得无缝。他们帮助我们克服了本地招聘挑战，并为跨境部署提供了端到端支持，确保所有流程都合规高效。他们是我们国际扩张努力中的可靠合作伙伴。" —— 客户公司HR负责人'
            },
            {
              id: 's5',
              title: '一家全球区块链技术平台 —— 全面的全球EOR支持',
              englishTitle: 'A Global Blockchain Technology Platform – Comprehensive Global EOR Support',
              image: '/images/index/case5.png',
              background: '作为区块链技术的全球领导者，该公司专注于构建去中心化互联网基础设施，同时持续吸引全球顶尖的技术和运营人才。随着业务快速扩张，公司需要在多个国家雇佣员工以支持全球运营，但面临重大挑战：',
              challenges: [
                '多国合规：不同国家复杂的劳动法、税收法规和社会福利政策要求完全合规的全球招聘流程',
                '效率和成本优化：快速扩张要求快速招聘和入职全球团队，同时最小化行政和管理成本',
                '多币种薪资管理：处理多种货币的薪资由于汇率波动带来财务风险'
              ],
              solutions: [
                {
                  title: '合规管理',
                  items: [
                    '利用SmartDeer的全球网络在不同国家合法雇佣员工',
                    '确保每个劳动合同和薪资流程都符合当地法规'
                  ]
                },
                {
                  title: '快速员工入职',
                  items: [
                    '促进高效入职，包括合同签署、社会保险登记和税务处理，使员工快速投入运营'
                  ]
                },
                {
                  title: '多币种薪资服务',
                  items: [
                    '通过SmartDeer平台支持150多种货币的薪资',
                    '集成汇率锁定功能，减轻货币波动的财务风险'
                  ]
                },
                {
                  title: '持续HR支持',
                  items: [
                    '提供持续的HR支持，包括政策咨询、员工管理和合同续签，减少客户HR团队的负担'
                  ]
                }
              ],
              results: [
                '快速全球扩张：成功帮助客户在三个月内在多个国家建立团队，支持其全球增长战略',
                '合规保证：确保所有招聘和薪资流程遵守当地法律，避免潜在的法律和税务风险',
                '成本节省30%：集中化HR管理减少了30%的行政成本和时间投入',
                '员工满意度提升：准确及时的薪资发放，加上强大的HR支持，提高了员工满意度和忠诚度'
              ],
              testimonial: '"SmartDeer的全球EOR服务让我们能够专注于核心业务，而不用担心在不同国家招聘和合规的复杂性。他们的专业支持和高效服务是我们全球运营的重要支柱。" —— 客户公司HR负责人'
            },
            {
              id: 's6',
              title: '一家领先的在线教育平台 —— 香港HRO服务简化HR管理',
              englishTitle: 'A Leading Online Education Platform – Hong Kong HRO Services for Streamlined HR Management',
              image: '/images/index/case6.png',
              background: '作为中国顶级在线教育平台之一，该公司专注于为全球学生提供高质量的教育资源。随着国际业务的扩张，公司在香港建立了运营中心以支持全球业务增长。然而，香港HR管理的特定要求和复杂性带来了几个挑战：',
              challenges: [
                '薪资和法定福利管理：处理薪资、强制性福利如MPF供款，并确保所有流程符合当地法规',
                '数据管理和合规：集中员工信息和薪资数据，同时遵守香港的隐私保护和劳动法',
                'HR效率提升：使用技术优化HR管理流程，降低行政成本并提高运营效率'
              ],
              solutions: [
                {
                  title: '薪资和福利管理服务',
                  items: [
                    'SmartDeer管理薪资计算和发放，确保准确及时的薪资支付',
                    '处理MPF供款和其他法定福利，确保完全符合法律要求并降低合规风险'
                  ]
                },
                {
                  title: 'HR SaaS平台支持',
                  items: [
                    '提供集成的HR SaaS平台，实现员工信息、考勤、薪资数据和合同记录的无缝管理',
                    '平台支持实时数据更新和自动报告生成，最小化人工错误并节省时间'
                  ]
                },
                {
                  title: '日常HR支持服务',
                  items: [
                    '提供政策咨询和运营支持，让客户了解香港劳动法和政策更新',
                    '协助入职和离职流程，确保所有行动都合法合规'
                  ]
                }
              ],
              results: [
                '管理效率提升40%：HR SaaS平台显著优化了薪资和数据管理工作流程，减少了HR团队的日常工作量',
                '零风险合规：专业的薪资和福利管理消除了法律风险，提高了员工满意度和忠诚度',
                '成本节省30%：外包HRO服务有效降低了运营HR成本，使公司能够将资源集中在核心业务增长上',
                '实时数据控制：集中化和数字化的员工数据管理为公司决策提供了高效支持'
              ],
              testimonial: '"SmartDeer的香港HRO服务和HR SaaS平台大大提高了我们的HR管理效率。他们在薪资和法定福利管理方面的专业性非常出色。SmartDeer是我们全球扩张努力中的重要合作伙伴。" —— 客户公司HR负责人'
            }
          ]
        }
      },
      en: {
        // 英文内容 - 简化版本，使用中文内容作为基础
        hero: {
          slogan: 'Global Hire & Global Employ',
          title: 'One-Stop Global HR Services',
          description: 'Recruit global talent and handle global employment compliance and payroll, providing professional one-stop HR service solutions.'
        },
        customer: {
          title: 'Serving Global Customers',
          subtitle: 'Serve Global Customers'
        },
        services: {
          title: 'Global HR Services & SaaS',
          subtitle: 'Global HR Services & SaaS',
          items: [
            {
              id: 'service-recruitment',
              title: 'Global Recruitment Services',
              subtitle: 'Global Recruitment',
              description: 'Professional talent acquisition services with global reach and 24/7 support.',
              image: '/images/index/recruitment.webp',
              alt: 'Global Recruitment Services'
            },
            {
              id: 'service-eor',
              title: 'Employer of Record',
              subtitle: 'Employer of Record',
              description: 'Compliant employment solutions without establishing legal entities.',
              image: '/images/index/eor.webp',
              alt: 'Employer of Record Services'
            },
            {
              id: 'service-contractor',
              title: 'Independent Contractor',
              subtitle: 'Independent Contractor',
              description: 'Flexible workforce solutions for short-term projects.',
              image: '/images/index/contractor.webp',
              alt: 'Independent Contractor Services'
            },
            {
              id: 'service-peo',
              title: 'HR Outsourcing',
              subtitle: 'Human Resource Outsourcing',
              description: 'Comprehensive HR outsourcing for established entities.',
              image: '/images/index/peo.webp',
              alt: 'HR Outsourcing Services'
            },
            {
              id: 'service-fintech',
              title: 'FinTech-Driven Payroll',
              subtitle: 'FinTech-Driven Global Payroll Solutions',
              description: 'Advanced payroll solutions supporting 150+ currencies.',
              image: '/images/index/fintech.png',
              alt: 'FinTech-Driven Solutions'
            },
            {
              id: 'service-saas',
              title: 'Global HR SaaS',
              subtitle: 'Global HR SaaS',
              description: 'Comprehensive HR management platform for global operations.',
              image: '/images/index/saas-video.mp4',
              alt: 'Global HR SaaS',
              isVideo: true
            }
          ]
        },
        advantages: {
          title: 'Core Advantages',
          subtitle: 'Why choose us?',
          items: [
            {
              icon: '/images/index/icon-global.svg',
              title: 'Global Coverage',
              description: 'Service network covering 150+ countries',
              alt: 'Global Coverage'
            },
            {
              icon: '/images/index/icon-professional-team.svg',
              title: 'Professional Compliance',
              description: 'Ensuring global policy and data compliance',
              alt: 'Professional Compliance Team'
            },
            {
              icon: '/images/index/icon-service.svg',
              title: '24/7 Response',
              description: '24/7 bilingual support service',
              alt: '24/7 Response'
            },
            {
              icon: '/images/index/icon-price.svg',
              title: 'Competitive Pricing',
              description: 'Highly competitive service fees',
              alt: 'Competitive Pricing'
            }
          ]
        },
        lifecycle: {
          title: 'Full-Life Cycle Management',
          subtitle: 'Full-Life Cycle Management',
          items: [
            { icon: '/images/index/recruitment.svg', title: 'Recruitment' },
            { icon: '/images/index/compliance.svg', title: 'Compliance' },
            { icon: '/images/index/contract.svg', title: 'Contract' },
            { icon: '/images/index/on-boarding.svg', title: 'Onboarding' },
            { icon: '/images/index/management.svg', title: 'Management' },
            { icon: '/images/index/payroll.svg', title: 'Payroll' },
            { icon: '/images/index/off-boarding.svg', title: 'Offboarding' }
          ]
        },
        process: {
          title: 'Service Process',
          subtitle: 'Professional service process to ensure quality delivery',
          items: [
            {
              title: 'Consultation & Assessment',
              description: 'Professional consultation and needs assessment',
              image: '/images/index/process-1.jpg'
            },
            {
              title: 'Solution Design',
              description: 'Customized solution design and planning',
              image: '/images/index/process-2.jpg'
            },
            {
              title: 'Implementation',
              description: 'Professional implementation and deployment',
              image: '/images/index/process-3.jpg'
            },
            {
              title: 'Support & Maintenance',
              description: 'Ongoing support and maintenance services',
              image: '/images/index/process-4.jpg'
            }
          ]
        },
        solution: {
          title: 'Solution Cases',
          subtitle: 'Real-world success stories and solutions',
          cases: [
            {
              id: 'case-tech-expansion',
              title: 'Global Tech Company Expansion',
              englishTitle: 'Global Tech Company Expansion',
              image: '/images/index/case-1.jpg',
              background: 'A leading technology company needed to expand operations to 15 countries across Asia, Europe, and Americas.',
              challenges: ['Multi-country compliance', 'Talent acquisition', 'Payroll management'],
              keyProblems: ['Complex regulatory requirements', 'Diverse talent markets', 'Currency management'],
              solutions: [
                {
                  title: 'EOR Services',
                  items: ['Legal compliance', 'Employment contracts', 'Local benefits']
                },
                {
                  title: 'Recruitment Support',
                  items: ['Local talent sourcing', 'Interview coordination', 'Onboarding process']
                }
              ],
              results: ['Successfully expanded to 15 countries', 'Reduced compliance risks by 90%', 'Accelerated hiring process by 60%'],
              testimonial: 'SmartDeer enabled our global expansion with seamless compliance and exceptional service quality.'
            }
          ]
        }
      },
      ja: {
        // 日文内容 - 简化版本
        hero: {
          slogan: 'グローバル採用・グローバル雇用',
          title: 'ワンストップグローバル人事サービス',
          description: 'グローバル人材の採用と雇用コンプライアンス、給与支払いを処理し、プロフェッショナルなワンストップ人事サービスソリューションを提供します。'
        },
        customer: {
          title: 'グローバル顧客へのサービス',
          subtitle: 'Serve Global Customers'
        },
        services: {
          title: 'グローバル人事サービス & SaaS',
          subtitle: 'Global HR Services & SaaS',
          items: [
            {
              id: 'service-recruitment',
              title: 'グローバル採用サービス',
              subtitle: 'Global Recruitment',
              description: 'グローバルリーチと24時間サポートを備えたプロフェッショナル人材獲得サービス。',
              image: '/images/index/recruitment.webp',
              alt: 'グローバル採用サービス'
            },
            {
              id: 'service-eor',
              title: '名義雇用主サービス',
              subtitle: 'Employer of Record',
              description: '法人設立なしでのコンプライアント雇用ソリューション。',
              image: '/images/index/eor.webp',
              alt: '名義雇用主サービス'
            },
            {
              id: 'service-contractor',
              title: '独立請負業者',
              subtitle: 'Independent Contractor',
              description: '短期プロジェクト向けの柔軟な労働力ソリューション。',
              image: '/images/index/contractor.webp',
              alt: '独立請負業者サービス'
            },
            {
              id: 'service-peo',
              title: '人事アウトソーシング',
              subtitle: 'Human Resource Outsourcing',
              description: '設立済み法人向けの包括的人事アウトソーシング。',
              image: '/images/index/peo.webp',
              alt: '人事アウトソーシングサービス'
            },
            {
              id: 'service-fintech',
              title: 'フィンテック給与システム',
              subtitle: 'FinTech-Driven Global Payroll Solutions',
              description: '150以上の通貨をサポートする先進的な給与ソリューション。',
              image: '/images/index/fintech.png',
              alt: 'フィンテック駆動ソリューション'
            },
            {
              id: 'service-saas',
              title: 'グローバル人事SaaS',
              subtitle: 'Global HR SaaS',
              description: 'グローバル運営のための包括的人事管理プラットフォーム。',
              image: '/images/index/saas-video.mp4',
              alt: 'グローバル人事SaaS',
              isVideo: true
            }
          ]
        },
        advantages: {
          title: 'コア優位性',
          subtitle: 'Why choose us?',
          items: [
            {
              icon: '/images/index/icon-global.svg',
              title: 'グローバルカバレッジ',
              description: '150以上の国をカバーするサービスネットワーク',
              alt: 'グローバルカバレッジ'
            },
            {
              icon: '/images/index/icon-professional-team.svg',
              title: 'プロフェッショナルコンプライアンス',
              description: 'グローバルポリシーとデータコンプライアンスの確保',
              alt: 'プロフェッショナルコンプライアンスチーム'
            },
            {
              icon: '/images/index/icon-service.svg',
              title: '24時間対応',
              description: '24時間バイリンガルサポートサービス',
              alt: '24時間対応'
            },
            {
              icon: '/images/index/icon-price.svg',
              title: '競争力のある価格',
              description: '非常に競争力のあるサービス料金',
              alt: '競争力のある価格'
            }
          ]
        },
        lifecycle: {
          title: 'フルライフサイクル管理',
          subtitle: 'Full-Life Cycle Management',
          items: [
            { icon: '/images/index/recruitment.svg', title: '採用' },
            { icon: '/images/index/compliance.svg', title: 'コンプライアンス' },
            { icon: '/images/index/contract.svg', title: '契約' },
            { icon: '/images/index/on-boarding.svg', title: 'オンボーディング' },
            { icon: '/images/index/management.svg', title: '管理' },
            { icon: '/images/index/payroll.svg', title: '給与' },
            { icon: '/images/index/off-boarding.svg', title: 'オフボーディング' }
          ]
        },
        process: {
          title: 'サービスプロセス',
          subtitle: '品質保証のためのプロフェッショナルサービスプロセス',
          items: [
            {
              title: 'コンサルテーション・評価',
              description: 'プロフェッショナルコンサルテーションとニーズ評価',
              image: '/images/index/process-1.jpg'
            },
            {
              title: 'ソリューション設計',
              description: 'カスタマイズされたソリューション設計と計画',
              image: '/images/index/process-2.jpg'
            },
            {
              title: '実装',
              description: 'プロフェッショナル実装と展開',
              image: '/images/index/process-3.jpg'
            },
            {
              title: 'サポート・メンテナンス',
              description: '継続的なサポートとメンテナンスサービス',
              image: '/images/index/process-4.jpg'
            }
          ]
        },
        solution: {
          title: 'ソリューション事例',
          subtitle: '実際の成功事例とソリューション',
          cases: [
            {
              id: 'case-tech-expansion',
              title: 'グローバルテック企業の拡張',
              englishTitle: 'Global Tech Company Expansion',
              image: '/images/index/case-1.jpg',
              background: '大手テクノロジー企業がアジア、ヨーロッパ、アメリカの15カ国に事業を拡大する必要がありました。',
              challenges: ['多国間コンプライアンス', '人材獲得', '給与管理'],
              keyProblems: ['複雑な規制要件', '多様な人材市場', '通貨管理'],
              solutions: [
                {
                  title: 'EORサービス',
                  items: ['法的コンプライアンス', '雇用契約', '現地福利厚生']
                },
                {
                  title: '採用サポート',
                  items: ['現地人材調達', '面接調整', 'オンボーディングプロセス']
                }
              ],
              results: ['15カ国への拡張成功', 'コンプライアンスリスク90%削減', '採用プロセス60%高速化'],
              testimonial: 'SmartDeerのおかげで、シームレスなコンプライアンスと優れたサービス品質でグローバル展開を実現できました。'
            }
          ]
        }
      }
    };

    return content[locale as keyof typeof content] || content.zh;
  };

  const content = getContent();

  return (
    <div className="index-page">
      {/* Header Banner */}
      <header ref={heroRef} className="header-banner bg-[#fff6ec]" >
        <div className="w-[1200px] mx-auto ">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div ref={heroTextRef} className="header-banner-text space-y-8 animate-fade-in-up">
              <h2 className="slogon text-3xl lg:text-4xl font-bold text-orange-500">
                {content.hero.slogan}
              </h2>
              <h1 className="title text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                {content.hero.title}
              </h1>
              <div className="desc space-y-6">
                <span className="text-xl lg:text-2xl text-gray-600 leading-relaxed block">
                  {content.hero.description}
                </span>
                <figure className="relative w-[140px] h-[140px]">
                  <Image
                    src="/images/index/global-desc.webp"
                    alt="Global Description"
                    width={140}
                    height={140}
                    className="object-contain"
                  />
                </figure>
              </div>
            </div>
            <div ref={heroImageRef} className="header-banner-image relative animate-fade-in-right min-h-[650px]">
              <figure className="global-min_1 absolute top-1/2 left-1/2 w-[598px] h-[600px] -mt-[300px] -ml-[299px] z-10">
                <Image
                  src="/images/index/global-min_1.png"
                  alt="Global Service 1"
                  width={598}
                  height={600}
                  className="w-full h-full transform rotate-45 animate-rotate-slow"
                />
              </figure>
              <figure className="global-min_2 absolute top-1/2 left-1/2 w-[480px] h-[480px] -mt-[240px] -ml-[240px] z-20">
                <Image
                  src="/images/index/global-min_2.png"
                  alt="Global Service 2"
                  width={480}
                  height={480}
                  className="w-full h-full animate-rotate"
                />
              </figure>
              <figure className="global-min_3 absolute top-1/2 left-1/2 w-[598px] h-[600px] -mt-[300px] -ml-[299px] z-30">
                <Image
                  src="/images/index/global-min_3.png"
                  alt="Global Service 3"
                  width={559}
                  height={556}
                  className="absolute top-[19px] -left-[10px]"
                />
              </figure>
            </div>
          </div>
        </div>
      </header>

      <main>
        {/* Customer Section */}
        <section className="customer py-20 lg:py-28 bg-white">
          <div className="max-w-7xl mx-auto ">
            <div className="section-title text-center mb-20">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                {content.customer.title}
              </h2>
              <p className="text-xl text-gray-600 tracking-wider">
                {content.customer.subtitle}
              </p>
            </div>
            <CustomerList />
          </div>
        </section>

        {/* Services Section */}
        <section className="service overflow-hidden bg-gray-50" style={{ minWidth: '1280px' }}>
          <div className="section-title text-center py-20">
            <h2 className="text-[48px] font-medium leading-[67px] text-gray-900 mb-4">
              {content.services.title}
            </h2>
            <p className="text-lg text-gray-400 tracking-[5px] mt-2 mb-0">
              {content.services.subtitle}
            </p>
          </div>

          <div className="service-list w-[1200px] mx-auto pb-16">
            {content.services.items.map((service: ServiceItem, index: number) => (
              <ServiceItemComponent
                key={service.id}
                service={service}
                index={index}
                onShowContactForm={() => setShowContactForm(true)}
              />
            ))}
          </div>
        </section>

        {/* Advantages Section */}
        <section className="advantages py-20 bg-gradient-to-b from-gray-50 to-white">
          <div className="w-[1200px] mx-auto px-4">
            <div className="section-title text-center mb-20">
              <h2 className="text-[48px] font-medium leading-[67px] text-gray-900 mb-6">
                {content.advantages.title}
              </h2>
              <p className="text-lg text-gray-400 tracking-[5px] mt-2 mb-0">
                {content.advantages.subtitle}
              </p>
            </div>

            <motion.div
              ref={advantageListRef}
              variants={staggerContainer}
              initial="hidden"
              animate={advantageListInView ? "visible" : "hidden"}
              className="advantage-list flex justify-between gap-6"
            >
              {content.advantages.items.map((advantage: AdvantageItem, index: number) => (
                <AdvantageItemComponent key={index} advantage={advantage} index={index} />
              ))}
            </motion.div>
          </div>
        </section>

        {/* Lifecycle Section */}
        <section className="lifecycle py-20 bg-gray-50">
          <div className="w-full">
            <div className="section-title text-center mb-20">
              <h2 className="text-[48px] font-medium leading-[67px] text-gray-900 mb-6">
                {content.lifecycle.title}
              </h2>
              <p className="text-lg text-gray-400 tracking-[5px] mt-2 mb-0">
                {content.lifecycle.subtitle}
              </p>
            </div>

            <motion.div
              ref={lifecycleListRef}
              variants={staggerContainer}
              initial="hidden"
              animate={lifecycleListInView ? "visible" : "hidden"}
              className="lifecycle-list flex justify-center items-end mb-[100px] w-full px-8"
            >
              {content.lifecycle.items.map((item: LifecycleItem, index: number) => (
                <LifecycleItemComponent key={index} item={item} index={index} />
              ))}
            </motion.div>

            <motion.div
              className="lifecycle-repeat w-[800px] mx-auto text-center"
              initial={{ opacity: 0, y: 30 }}
              animate={lifecycleListInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              <Image
                src="/images/index/repeat.svg"
                alt="Lifecycle Repeat"
                width={800}
                height={100}
                className="w-full h-auto"
              />
            </motion.div>
          </div>
        </section>

        {/* Process Section */}
        <section className="process bg-gradient-to-b from-white to-gray-50 overflow-hidden">
          <div className="section-title text-center py-20">
            <h2 className="text-[48px] font-medium leading-[67px] text-gray-900 mb-4">
              {content.process.title}
            </h2>
            <p className="text-lg text-gray-400 tracking-[5px] mt-2 mb-0">
              {content.process.subtitle}
            </p>
          </div>

          <div className="process-list w-full">
            {content.process.items.map((item: ProcessItem, index: number) => (
              <ProcessItemComponent key={index} item={item} index={index} />
            ))}
          </div>
        </section>

        {/* Solution Section */}
        <section id="solution" className="solution py-20 lg:py-28 bg-gradient-to-b from-gray-50 to-white">
          <div className="max-w-7xl mx-auto ">
            <div className="section-title text-center mb-20">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                {content.solution.title}
              </h2>
              <p className="text-xl text-gray-600 tracking-wider">
                {content.solution.subtitle}
              </p>
            </div>

            <div className="solution-list space-y-16">
              {content.solution.cases.map((caseItem: SolutionCase, index: number) => (
                <SolutionItemComponent
                  key={caseItem.id}
                  caseItem={caseItem}
                  index={index}
                  solutionExpandStatus={solutionExpandStatus}
                  toggleSolution={toggleSolution}
                  getSolutionDescStyle={getSolutionDescStyle}
                />
              ))}

            </div>
          </div>
        </section>
      </main>

      {/* Contact Form */}
      {showContactForm && (
        <ContactForm 
          locale={locale}
          onClose={() => setShowContactForm(false)}
          onSubmit={() => {
            setShowContactForm(false);
            // Handle form submission success
          }}
        />
      )}

      {/* Floating Elements */}
      {/* Consultant */}
      <div className="anchor fixed bottom-32 right-8 z-50">
        <div 
          className="consultant cursor-pointer"
          onClick={() => setShowConsultantCode(!showConsultantCode)}
        >
          <figure className="w-16 h-16">
            <Image
              src="/images/index/anchor-avatar.png"
              alt="Consultant"
              width={64}
              height={64}
              className="w-full h-full rounded-full shadow-lg"
            />
          </figure>
        </div>
        {showConsultantCode && (
          <div className="consultant-code absolute bottom-20 right-0 bg-white p-4 rounded-lg shadow-lg">
            <div 
              className="close absolute top-2 right-2 cursor-pointer"
              onClick={() => setShowConsultantCode(false)}
            >
              ×
            </div>
            <figure className="w-32 h-32">
              <Image
                src="/images/index/anchor-code.png"
                alt="Consultant QR Code"
                width={128}
                height={128}
                className="w-full h-full"
              />
            </figure>
          </div>
        )}
      </div>

      {/* Bot Button */}
      <div 
        className="bot-container fixed bottom-16 right-8 cursor-pointer z-50"
        onClick={toggleChat}
      >
        <Image
          src="/images/index/bot_logo_zh.png"
          alt="Bot"
          width={60}
          height={60}
          className="w-15 h-15 rounded-full shadow-lg"
        />
      </div>

      {/* Go Top */}
      <div
        className="go-top-container fixed bottom-4 right-8 cursor-pointer z-50"
        onClick={() => smoothScrollTo(500, 0)}
      >
        <Image
          src="/images/index/top_icon.png"
          alt="Go Top"
          width={40}
          height={40}
          className="w-10 h-10 rounded-full shadow-lg"
        />
      </div>

      {/* 滚动到顶部按钮 */}
      <ScrollToTop />
    </div>
  );
}